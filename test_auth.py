#!/usr/bin/env python3
"""
Test script to verify Google API authentication setup.
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.auth import verify_authentication


def main():
    """Test authentication and provide setup instructions."""
    print("🔐 Testing Google API Authentication")
    print("=" * 50)
    
    if verify_authentication():
        print("\n✅ SUCCESS: Authentication is working!")
        print("\nYou can now run the main script:")
        print("  uv run python main.py --help")
    else:
        print("\n❌ FAILED: Authentication not working")
        print("\n📋 Setup Instructions:")
        print("1. Go to Google Cloud Console (https://console.cloud.google.com/)")
        print("2. Create a new project or select an existing one")
        print("3. Enable the following APIs:")
        print("   - Google Drive API")
        print("   - Google Sheets API")
        print("4. Go to 'Credentials' and create OAuth 2.0 Client ID")
        print("5. Choose 'Desktop application' as the application type")
        print("6. Download the credentials JSON file")
        print("7. Save it as 'credentials.json' in this project directory")
        print("8. Run this script again to test")
        
        print("\n🔗 Quick Links:")
        print("- Google Cloud Console: https://console.cloud.google.com/")
        print("- Enable APIs: https://console.cloud.google.com/apis/library")
        print("- Create Credentials: https://console.cloud.google.com/apis/credentials")
        
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
