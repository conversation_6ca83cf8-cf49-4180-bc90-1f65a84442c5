"""
Image matching logic to connect SKUs with Drive images.
"""
import re
from typing import Dict, List, Optional, Set
from difflib import SequenceMatcher


class ImageMatcher:
    """Matches SKU data with Google Drive images."""
    
    def __init__(self):
        self.style_aliases = {
            'SOLANA': ['<PERSON><PERSON><PERSON><PERSON>'],
            '<PERSON><PERSON>INITAS': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
            'DEL_MAR': ['<PERSON><PERSON><PERSON>', 'DEL_MAR', 'DEL-MAR'],
            'LEUCADIA': ['LEUCAD', 'LEUCADIA'],
            'NORFOLK': ['NORFOL', 'NORFOLK'],
            'HELENA': ['HELENA'],
            'EXPLORER': ['EXPLOR', 'EXPLORER']
        }
        
        self.color_aliases = {
            'SALMON': ['SALM', 'SALMON'],
            'BURGUNDY': ['BURG', 'BURGUNDY'],
            'RED': ['RED'],
            'LIGHT_PINK': ['<PERSON><PERSON>INK', 'LIGHT_PINK', 'L<PERSON><PERSON><PERSON>NK'],
            '<PERSON><PERSON><PERSON>_PINK': ['D<PERSON><PERSON>N<PERSON>', '<PERSON><PERSON><PERSON>_<PERSON>INK', '<PERSON><PERSON><PERSON><PERSON>NK'],
            'HOT_PINK': ['HTPINK', 'HOT_PINK', 'HOTPINK'],
            'CORAL': ['CORL', 'CORAL'],
            'MINT_GREEN': ['MNTGRN', 'MINT_GREEN', 'MINTGREEN'],
            'EMERALD': ['EMRD', 'EMERALD'],
            'LIGHT_GREEN': ['LTGR', 'LIGHT_GREEN', 'LIGHTGREEN'],
            'TEAL': ['TEAL'],
            'MOSS_GREEN': ['MOSSG', 'MOSS_GREEN', 'MOSSGREEN', 'MOSS'],
            'DARK_BLUE': ['DKBLU', 'DARK_BLUE', 'DARKBLUE', 'DARBLU'],
            'BRIGHT_BLUE': ['BRBLU', 'BRIGHT_BLUE', 'BRIGHTBLUE'],
            'POWDER_BLUE': ['POWBLU', 'POWDER_BLUE', 'POWDERBLUE'],
            'HOT_BLUE': ['HOTBLU', 'HOT_BLUE', 'HOTBLUE'],
            'NAVY_BLUE': ['NAVBLU', 'NAVY_BLUE', 'NAVYBLUE'],
            'DARK_GREY': ['DKGREY', 'DARK_GREY', 'DARKGREY', 'DKGRY'],
            'LIGHT_GREY': ['LTGRY', 'LIGHT_GREY', 'LIGHTGREY'],
            'GREY': ['GRY', 'GREY', 'GRAY'],
            'BEIGE': ['BEIG', 'BEIGE', 'BEI'],
            'TAN': ['TAN'],
            'DARK_BROWN': ['DKBRN', 'DARK_BROWN', 'DARKBROWN', 'DRKBRN'],
            'LIGHT_BROWN': ['LTBRN', 'LIGHT_BROWN', 'LIGHTBROWN'],
            'CHESTNUT': ['CHES', 'CHESTNUT', 'CHESNUT', 'CHE'],
            'BLACK': ['BLK', 'BLACK'],
            'BONE': ['BONE', 'BON'],
            'CREAM': ['CREM', 'CREAM'],
            'GUN_METAL': ['GUNMET', 'GUN_METAL', 'GUNMETAL'],
            'LIGHT_BLUE_GREY': ['LTBLUGRY', 'LIGHT_BLUE_GREY'],
            'BLUE_GREY': ['BLUGRY', 'BLUE_GREY', 'BLUEGREY'],
            'RUSTY_ORANGE': ['RUSTOR', 'RUSTY_ORANGE'],
            'STONE': ['STON', 'STONE'],
            'RUST': ['RUST'],
            'LAVENDER': ['LAVN', 'LAVENDER'],
            'VIOLET': ['VIOL', 'VIOLET'],
            'LIGHT_MOSS': ['LTMOS', 'LIGHT_MOSS'],
            'MUSTARD': ['MUS', 'MUSTARD'],
            'PINK': ['PINK'],
            'LIGHT_BROWN_GREY': ['LTBRNGRY', 'LIGHT_BROWN_GREY'],
            'MUD': ['MUD']
        }
    
    def normalize_style(self, style: str) -> str:
        """Normalize style name for matching."""
        style = style.upper().strip()
        
        # Check aliases
        for canonical, aliases in self.style_aliases.items():
            if style in aliases:
                return canonical
        
        return style
    
    def normalize_color(self, color: str) -> str:
        """Normalize color name for matching."""
        color = color.upper().strip()
        
        # Remove special characters and normalize
        color = re.sub(r'[^A-Z0-9_]', '_', color)
        color = re.sub(r'_+', '_', color).strip('_')
        
        # Check aliases
        for canonical, aliases in self.color_aliases.items():
            if color in aliases:
                return canonical
        
        return color
    
    def fuzzy_match_color(self, color1: str, color2: str, threshold: float = 0.8) -> bool:
        """
        Perform fuzzy matching between two color names.
        
        Args:
            color1: First color name
            color2: Second color name
            threshold: Similarity threshold (0-1)
            
        Returns:
            True if colors match above threshold
        """
        # Normalize both colors
        norm1 = self.normalize_color(color1)
        norm2 = self.normalize_color(color2)
        
        # Direct match
        if norm1 == norm2:
            return True
        
        # Fuzzy match
        similarity = SequenceMatcher(None, norm1, norm2).ratio()
        return similarity >= threshold
    
    def match_sku_to_images(self, sku_data: Dict, categorized_images: Dict) -> Dict[str, str]:
        """
        Match a single SKU row to available images.
        
        Args:
            sku_data: Dictionary containing SKU information
            categorized_images: Nested dict of {style: {color: {type: link}}}
            
        Returns:
            Dictionary with image links for each type
        """
        style = sku_data.get('Style', '').strip()
        color = sku_data.get('Color', '').strip()
        
        if not style or not color:
            return {}
        
        # Normalize for matching
        norm_style = self.normalize_style(style)
        norm_color = self.normalize_color(color)
        
        # Find matching style in images
        matching_style = None
        for img_style in categorized_images.keys():
            if self.normalize_style(img_style) == norm_style:
                matching_style = img_style
                break
        
        if not matching_style:
            return {}
        
        # Find matching color in images
        matching_color = None
        style_images = categorized_images[matching_style]
        
        for img_color in style_images.keys():
            if self.fuzzy_match_color(color, img_color):
                matching_color = img_color
                break
        
        if not matching_color:
            return {}
        
        # Return available image links
        color_images = style_images[matching_color]
        result = {}
        
        # Map image types to CSV columns
        type_mapping = {
            'ANGLE': 'IMG-Angle',
            'FRONT': 'IMG-Front',
            'TOP': 'IMG-Top',
            'LIFESTYLE': 'IMG-Lifestyle'
        }
        
        for img_type, csv_column in type_mapping.items():
            if img_type in color_images:
                result[csv_column] = color_images[img_type]
        
        return result
    
    def match_all_skus(self, sku_rows: List[Dict], categorized_images: Dict) -> List[Dict]:
        """
        Match all SKU rows to available images.
        
        Args:
            sku_rows: List of SKU dictionaries
            categorized_images: Nested dict of {style: {color: {type: link}}}
            
        Returns:
            List of SKU dictionaries with image links added
        """
        matched_rows = []
        match_stats = {'total': 0, 'matched': 0, 'angle': 0, 'front': 0, 'top': 0, 'lifestyle': 0}
        
        for row in sku_rows:
            match_stats['total'] += 1
            
            # Skip empty rows
            if not row.get('Style') or not row.get('Color'):
                matched_rows.append(row)
                continue
            
            # Get image matches
            image_matches = self.match_sku_to_images(row, categorized_images)
            
            # Update row with image links
            updated_row = row.copy()
            for img_column, link in image_matches.items():
                updated_row[img_column] = link
                match_stats[img_column.split('-')[1].lower()] += 1
            
            if image_matches:
                match_stats['matched'] += 1
            
            matched_rows.append(updated_row)
        
        # Print matching statistics
        print(f"\nMatching Statistics:")
        print(f"Total SKUs: {match_stats['total']}")
        print(f"SKUs with matches: {match_stats['matched']}")
        print(f"Angle images: {match_stats['angle']}")
        print(f"Front images: {match_stats['front']}")
        print(f"Top images: {match_stats['top']}")
        print(f"Lifestyle images: {match_stats['lifestyle']}")
        
        return matched_rows
    
    def find_unmatched_images(self, sku_rows: List[Dict], categorized_images: Dict) -> Dict:
        """
        Find images that don't match any SKU.
        
        Args:
            sku_rows: List of SKU dictionaries
            categorized_images: Nested dict of {style: {color: {type: link}}}
            
        Returns:
            Dictionary of unmatched images
        """
        # Create set of all SKU style-color combinations
        sku_combinations = set()
        for row in sku_rows:
            style = row.get('Style', '').strip()
            color = row.get('Color', '').strip()
            if style and color:
                norm_style = self.normalize_style(style)
                norm_color = self.normalize_color(color)
                sku_combinations.add((norm_style, norm_color))
        
        # Find unmatched images
        unmatched = {}
        for img_style, colors in categorized_images.items():
            norm_img_style = self.normalize_style(img_style)
            
            for img_color, types in colors.items():
                norm_img_color = self.normalize_color(img_color)
                
                # Check if this combination exists in SKUs
                found_match = False
                for sku_style, sku_color in sku_combinations:
                    if (norm_img_style == sku_style and 
                        self.fuzzy_match_color(img_color, sku_color)):
                        found_match = True
                        break
                
                if not found_match:
                    if img_style not in unmatched:
                        unmatched[img_style] = {}
                    unmatched[img_style][img_color] = types
        
        return unmatched
