"""
Google Drive folder scanner for hat images.
"""
import re
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, parse_qs

from googleapiclient.discovery import Resource

from .auth import get_drive_service


class DriveScanner:
    """Scanner for Google Drive folders to find hat images."""
    
    def __init__(self):
        self.service = get_drive_service()
        self.image_cache = {}
    
    def extract_folder_id(self, drive_url: str) -> str:
        """
        Extract folder ID from Google Drive URL.
        
        Args:
            drive_url: Google Drive folder URL
            
        Returns:
            Folder ID string
        """
        # Handle different URL formats
        if '/folders/' in drive_url:
            folder_id = drive_url.split('/folders/')[1].split('?')[0]
        else:
            # Try to extract from URL parameters
            parsed = urlparse(drive_url)
            params = parse_qs(parsed.query)
            folder_id = params.get('id', [None])[0]
        
        if not folder_id:
            raise ValueError(f"Could not extract folder ID from URL: {drive_url}")
        
        return folder_id
    
    def scan_folder(self, folder_id: str) -> List[Dict]:
        """
        Recursively scan a Google Drive folder for images.

        Args:
            folder_id: Google Drive folder ID

        Returns:
            List of image file metadata dictionaries
        """
        return self._scan_folder_recursive(folder_id, "")

    def _scan_folder_recursive(self, folder_id: str, path: str) -> List[Dict]:
        """
        Recursively scan a folder and its subfolders for images.

        Args:
            folder_id: Google Drive folder ID
            path: Current path for logging purposes

        Returns:
            List of image file metadata dictionaries
        """
        images = []
        folders_to_scan = []
        page_token = None

        print(f"Scanning folder: {path or 'root'}")

        while True:
            # Query for all files and folders in the current folder
            query = f"'{folder_id}' in parents and trashed=false"

            results = self.service.files().list(
                q=query,
                pageSize=1000,
                fields="nextPageToken, files(id, name, mimeType, size, webViewLink, webContentLink)",
                pageToken=page_token
            ).execute()

            files = results.get('files', [])

            for file in files:
                mime_type = file.get('mimeType', '')
                file_name = file['name']

                # Check if it's a folder
                if mime_type == 'application/vnd.google-apps.folder':
                    folders_to_scan.append({
                        'id': file['id'],
                        'name': file_name,
                        'path': f"{path}/{file_name}" if path else file_name
                    })
                # Check if it's an image file
                elif (mime_type.startswith('image/') or
                      file_name.lower().endswith(('.webp', '.png', '.jpg', '.jpeg'))):

                    # Generate webp link
                    webp_link = self.get_webp_link(file['id'])

                    image_info = {
                        'id': file['id'],
                        'name': file_name,
                        'path': f"{path}/{file_name}" if path else file_name,
                        'mime_type': mime_type,
                        'size': file.get('size', 0),
                        'web_view_link': file.get('webViewLink', ''),
                        'web_content_link': file.get('webContentLink', ''),
                        'webp_link': webp_link
                    }
                    images.append(image_info)

            page_token = results.get('nextPageToken')
            if not page_token:
                break

        # Recursively scan subfolders
        for folder in folders_to_scan:
            subfolder_images = self._scan_folder_recursive(folder['id'], folder['path'])
            images.extend(subfolder_images)

        if not path:  # Only print total for root call
            print(f"Found {len(images)} total images across all folders")

        return images
    
    def get_webp_link(self, file_id: str) -> str:
        """
        Generate a webp format link for an image file.
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Direct link to webp version of the image
        """
        # Google Drive direct link format for webp
        return f"https://drive.google.com/uc?export=view&id={file_id}"
    
    def parse_filename(self, filename: str) -> Optional[Dict]:
        """
        Parse hat image filename to extract style, color, and image type.

        Expected formats based on directory structure:
        - hat-{style}-{color}-{type}.webp (e.g., hat-solana-salmon-Angle.webp)
        - hat-{style}-{color}{type}.webp (e.g., hat-delmar-navy_blueAngle.webp)
        - hat-{style}-{color}-{type}.webp (e.g., hat-norfolk-dark-brownAngle.webp)

        Args:
            filename: Image filename

        Returns:
            Dictionary with parsed components or None if parsing fails
        """
        # Remove file extension
        name_without_ext = filename.rsplit('.', 1)[0]

        # Handle special case first: hat-style-color1-color2Type (e.g., hat-norfolk-dark-brownAngle)
        special_combined_pattern = r'^hat-([a-z_]+)-([a-z]+)-([a-z]+)([A-Z][a-z]+)$'
        special_match = re.match(special_combined_pattern, name_without_ext)
        if special_match:
            style, color_part1, color_part2, image_type = special_match.groups()

            # Check if the last part is a valid image type
            if image_type in ['Angle', 'Front', 'Top']:
                color = f"{color_part1}_{color_part2}".upper()
                style = style.upper()
                image_type = image_type.upper()

                return {
                    'style': style,
                    'color': color,
                    'image_type': image_type,
                    'original_filename': filename
                }

        # Try different patterns based on observed structure
        patterns = [
            # Standard format: hat-style-color-type
            r'^hat-([a-z_]+)-([a-z_]+)-([A-Za-z]+)$',
            # Format with dash in color: hat-style-color-part-type (more specific)
            r'^hat-([a-z_]+)-([a-z_]+)-([a-z_]+)-([A-Za-z]+)$',
            # Format without dash before type: hat-style-colortype
            r'^hat-([a-z_]+)-([a-z_]+)([A-Za-z]+)$',
            # Legacy formats (uppercase)
            r'^HAT-([A-Z_]+)-([A-Z_]+)-([A-Z]+)$',
            r'^([A-Z_]+)-([A-Z_]+)-([A-Z]+)$',
        ]

        for pattern in patterns:
            match = re.match(pattern, name_without_ext)
            if match:
                groups = match.groups()

                if len(groups) == 4:
                    # Pattern with 4 groups: hat-style-color-part-type
                    style, color_part1, color_part2, image_type = groups
                    color = f"{color_part1}_{color_part2}".upper()
                    style = style.upper().replace('-', '_')
                else:
                    # Pattern with 3 groups: hat-style-color-type
                    style, color, image_type = groups
                    # Normalize style name
                    style = style.upper().replace('-', '_')
                    # Normalize color name
                    color = color.upper().replace('-', '_')

                # Normalize image type
                image_type = image_type.upper()
                if image_type in ['ANGLE', 'ANGLED']:
                    image_type = 'ANGLE'
                elif image_type in ['FRONT', 'FORWARD']:
                    image_type = 'FRONT'
                elif image_type in ['TOP', 'ABOVE']:
                    image_type = 'TOP'
                elif image_type in ['LIFESTYLE', 'LIFE']:
                    image_type = 'LIFESTYLE'

                return {
                    'style': style,
                    'color': color,
                    'image_type': image_type,
                    'original_filename': filename
                }

        # Fallback: try to parse manually for any remaining edge cases
        if name_without_ext.startswith('hat-'):
            parts = name_without_ext[4:].split('-')  # Remove 'hat-' prefix
            if len(parts) >= 2:
                # Try to extract image type from the end
                last_part = parts[-1]

                # Check if last part ends with a known image type
                for img_type in ['Angle', 'Front', 'Top', 'angle', 'front', 'top']:
                    if last_part.endswith(img_type):
                        # Extract the image type
                        image_type = img_type.upper()
                        # Remove image type from last part to get color
                        color_part = last_part[:-len(img_type)]

                        # Reconstruct color from parts
                        if color_part:
                            color_parts = parts[1:-1] + [color_part]
                        else:
                            color_parts = parts[1:-1]

                        color = '_'.join(color_parts).upper()
                        style = parts[0].upper()

                        return {
                            'style': style,
                            'color': color,
                            'image_type': image_type,
                            'original_filename': filename
                        }

        return None
    
    def categorize_images(self, images: List[Dict]) -> Dict[str, Dict[str, Dict[str, str]]]:
        """
        Categorize images by style, color, and type.

        Args:
            images: List of image metadata dictionaries

        Returns:
            Nested dictionary: {style: {color: {image_type: webp_link}}}
        """
        categorized = {}
        parsed_count = 0

        for image in images:
            parsed = self.parse_filename(image['name'])
            if not parsed:
                # Skip common non-image files
                if image['name'] in ['.DS_Store', 'SKUs', 'styles'] or image['name'].endswith('.psd'):
                    continue
                print(f"Warning: Could not parse filename: {image['name']} (path: {image.get('path', 'unknown')})")
                continue

            parsed_count += 1
            style = parsed['style']
            color = parsed['color']
            image_type = parsed['image_type']

            if style not in categorized:
                categorized[style] = {}
            if color not in categorized[style]:
                categorized[style][color] = {}

            # Use webp link
            categorized[style][color][image_type] = image['webp_link']

        print(f"Successfully parsed {parsed_count} out of {len(images)} files")
        return categorized


def main():
    """Test the drive scanner."""
    scanner = DriveScanner()
    
    # Test with the provided folder URL
    folder_url = "https://drive.google.com/drive/folders/1RrUSrvgUfEyO3zvwqsNXmYZUPd08ZDGs?usp=sharing"
    folder_id = scanner.extract_folder_id(folder_url)
    
    print(f"Scanning folder ID: {folder_id}")
    images = scanner.scan_folder(folder_id)
    
    print(f"\nFound {len(images)} images")
    
    # Show first few images
    for i, image in enumerate(images[:5]):
        print(f"{i+1}. {image['name']} -> {image['webp_link']}")
    
    # Categorize images
    categorized = scanner.categorize_images(images)
    print(f"\nCategorized into {len(categorized)} styles")
    
    for style, colors in categorized.items():
        print(f"  {style}: {len(colors)} colors")


if __name__ == "__main__":
    main()
