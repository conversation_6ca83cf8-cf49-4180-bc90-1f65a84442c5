"""
Google Drive folder scanner for hat images.
"""
import re
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, parse_qs

from googleapiclient.discovery import Resource

from .auth import get_drive_service


class DriveScanner:
    """Scanner for Google Drive folders to find hat images."""
    
    def __init__(self):
        self.service = get_drive_service()
        self.image_cache = {}
    
    def extract_folder_id(self, drive_url: str) -> str:
        """
        Extract folder ID from Google Drive URL.
        
        Args:
            drive_url: Google Drive folder URL
            
        Returns:
            Folder ID string
        """
        # Handle different URL formats
        if '/folders/' in drive_url:
            folder_id = drive_url.split('/folders/')[1].split('?')[0]
        else:
            # Try to extract from URL parameters
            parsed = urlparse(drive_url)
            params = parse_qs(parsed.query)
            folder_id = params.get('id', [None])[0]
        
        if not folder_id:
            raise ValueError(f"Could not extract folder ID from URL: {drive_url}")
        
        return folder_id
    
    def scan_folder(self, folder_id: str) -> List[Dict]:
        """
        Scan a Google Drive folder for images.
        
        Args:
            folder_id: Google Drive folder ID
            
        Returns:
            List of image file metadata dictionaries
        """
        images = []
        page_token = None
        
        while True:
            # Query for image files in the folder
            query = f"'{folder_id}' in parents and (mimeType contains 'image/' or name contains '.webp' or name contains '.png' or name contains '.jpg' or name contains '.jpeg')"
            
            results = self.service.files().list(
                q=query,
                pageSize=1000,
                fields="nextPageToken, files(id, name, mimeType, size, webViewLink, webContentLink)",
                pageToken=page_token
            ).execute()
            
            files = results.get('files', [])
            
            for file in files:
                # Generate webp link if possible
                webp_link = self.get_webp_link(file['id'])
                
                image_info = {
                    'id': file['id'],
                    'name': file['name'],
                    'mime_type': file.get('mimeType', ''),
                    'size': file.get('size', 0),
                    'web_view_link': file.get('webViewLink', ''),
                    'web_content_link': file.get('webContentLink', ''),
                    'webp_link': webp_link
                }
                images.append(image_info)
            
            page_token = results.get('nextPageToken')
            if not page_token:
                break
        
        print(f"Found {len(images)} images in folder")
        return images
    
    def get_webp_link(self, file_id: str) -> str:
        """
        Generate a webp format link for an image file.
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Direct link to webp version of the image
        """
        # Google Drive direct link format for webp
        return f"https://drive.google.com/uc?export=view&id={file_id}"
    
    def parse_filename(self, filename: str) -> Optional[Dict]:
        """
        Parse hat image filename to extract style, color, and image type.
        
        Expected formats:
        - HAT-STYLE-COLOR-TYPE.ext (e.g., HAT-SOLANA-SALMON-ANGLE.webp)
        - STYLE-COLOR-TYPE.ext (e.g., SOLANA-SALMON-ANGLE.webp)
        
        Args:
            filename: Image filename
            
        Returns:
            Dictionary with parsed components or None if parsing fails
        """
        # Remove file extension
        name_without_ext = filename.rsplit('.', 1)[0]
        
        # Try different patterns
        patterns = [
            r'^HAT-([A-Z]+)-([A-Z_]+)-([A-Z]+)$',  # HAT-STYLE-COLOR-TYPE
            r'^([A-Z]+)-([A-Z_]+)-([A-Z]+)$',      # STYLE-COLOR-TYPE
            r'^HAT-([A-Z]+)-([A-Z_]+)_([A-Z]+)$',  # HAT-STYLE-COLOR_TYPE
            r'^([A-Z]+)-([A-Z_]+)_([A-Z]+)$',      # STYLE-COLOR_TYPE
        ]
        
        for pattern in patterns:
            match = re.match(pattern, name_without_ext.upper())
            if match:
                style, color, image_type = match.groups()
                
                # Normalize image type
                image_type = image_type.upper()
                if image_type in ['ANGLE', 'ANGLED']:
                    image_type = 'ANGLE'
                elif image_type in ['FRONT', 'FORWARD']:
                    image_type = 'FRONT'
                elif image_type in ['TOP', 'ABOVE']:
                    image_type = 'TOP'
                elif image_type in ['LIFESTYLE', 'LIFE']:
                    image_type = 'LIFESTYLE'
                
                return {
                    'style': style,
                    'color': color,
                    'image_type': image_type,
                    'original_filename': filename
                }
        
        # If no pattern matches, try to extract what we can
        parts = name_without_ext.upper().split('-')
        if len(parts) >= 3:
            # Assume last part is image type, second to last is color
            image_type = parts[-1]
            color = parts[-2]
            style = '-'.join(parts[:-2]).replace('HAT-', '')
            
            return {
                'style': style,
                'color': color,
                'image_type': image_type,
                'original_filename': filename
            }
        
        return None
    
    def categorize_images(self, images: List[Dict]) -> Dict[str, Dict[str, Dict[str, str]]]:
        """
        Categorize images by style, color, and type.
        
        Args:
            images: List of image metadata dictionaries
            
        Returns:
            Nested dictionary: {style: {color: {image_type: webp_link}}}
        """
        categorized = {}
        
        for image in images:
            parsed = self.parse_filename(image['name'])
            if not parsed:
                print(f"Warning: Could not parse filename: {image['name']}")
                continue
            
            style = parsed['style']
            color = parsed['color']
            image_type = parsed['image_type']
            
            if style not in categorized:
                categorized[style] = {}
            if color not in categorized[style]:
                categorized[style][color] = {}
            
            # Use webp link
            categorized[style][color][image_type] = image['webp_link']
        
        return categorized


def main():
    """Test the drive scanner."""
    scanner = DriveScanner()
    
    # Test with the provided folder URL
    folder_url = "https://drive.google.com/drive/folders/1RrUSrvgUfEyO3zvwqsNXmYZUPd08ZDGs?usp=sharing"
    folder_id = scanner.extract_folder_id(folder_url)
    
    print(f"Scanning folder ID: {folder_id}")
    images = scanner.scan_folder(folder_id)
    
    print(f"\nFound {len(images)} images")
    
    # Show first few images
    for i, image in enumerate(images[:5]):
        print(f"{i+1}. {image['name']} -> {image['webp_link']}")
    
    # Categorize images
    categorized = scanner.categorize_images(images)
    print(f"\nCategorized into {len(categorized)} styles")
    
    for style, colors in categorized.items():
        print(f"  {style}: {len(colors)} colors")


if __name__ == "__main__":
    main()
