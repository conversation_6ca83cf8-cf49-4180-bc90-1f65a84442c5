"""
Google API Authentication module for Drive and Sheets access.
"""
import os
import pickle
from pathlib import Path
from typing import Optional

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build


# Scopes required for Drive and Sheets access
SCOPES = [
    'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/spreadsheets'
]

# File paths for credentials
CREDENTIALS_FILE = 'credentials.json'
TOKEN_FILE = 'token.pickle'


def get_credentials() -> Optional[Credentials]:
    """
    Get valid Google API credentials.
    
    Returns:
        Credentials object if successful, None otherwise
    """
    creds = None
    
    # Load existing token if available
    if os.path.exists(TOKEN_FILE):
        with open(TOKEN_FILE, 'rb') as token:
            creds = pickle.load(token)
    
    # If there are no (valid) credentials available, let the user log in
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
            except Exception as e:
                print(f"Error refreshing credentials: {e}")
                creds = None
        
        if not creds:
            if not os.path.exists(CREDENTIALS_FILE):
                print(f"Error: {CREDENTIALS_FILE} not found!")
                print("Please download your OAuth 2.0 credentials from Google Cloud Console")
                print("and save them as 'credentials.json' in the project root.")
                return None
            
            flow = InstalledAppFlow.from_client_secrets_file(
                CREDENTIALS_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
        
        # Save the credentials for the next run
        with open(TOKEN_FILE, 'wb') as token:
            pickle.dump(creds, token)
    
    return creds


def get_drive_service():
    """
    Get authenticated Google Drive service.
    
    Returns:
        Google Drive service object
    """
    creds = get_credentials()
    if not creds:
        raise Exception("Failed to get valid credentials")
    
    return build('drive', 'v3', credentials=creds)


def get_sheets_service():
    """
    Get authenticated Google Sheets service.
    
    Returns:
        Google Sheets service object
    """
    creds = get_credentials()
    if not creds:
        raise Exception("Failed to get valid credentials")
    
    return build('sheets', 'v4', credentials=creds)


def verify_authentication():
    """
    Verify that authentication is working by testing Drive access.
    
    Returns:
        bool: True if authentication successful, False otherwise
    """
    try:
        service = get_drive_service()
        # Test by getting user info
        about = service.about().get(fields="user").execute()
        user = about.get('user', {})
        print(f"Authentication successful! Logged in as: {user.get('displayName', 'Unknown')}")
        print(f"Email: {user.get('emailAddress', 'Unknown')}")
        return True
    except Exception as e:
        print(f"Authentication failed: {e}")
        return False


if __name__ == "__main__":
    # Test authentication
    print("Testing Google API authentication...")
    if verify_authentication():
        print("✅ Authentication setup complete!")
    else:
        print("❌ Authentication failed. Please check your credentials.")
