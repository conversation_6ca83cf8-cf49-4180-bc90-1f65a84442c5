"""
Google Sheets integration for updating remote spreadsheet.
"""
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs

from .auth import get_sheets_service


class SheetsUpdater:
    """Updates Google Sheets with SKU and image data."""
    
    def __init__(self):
        self.service = get_sheets_service()
    
    def extract_spreadsheet_id(self, sheets_url: str) -> str:
        """
        Extract spreadsheet ID from Google Sheets URL.
        
        Args:
            sheets_url: Google Sheets URL
            
        Returns:
            Spreadsheet ID string
        """
        if '/spreadsheets/d/' in sheets_url:
            spreadsheet_id = sheets_url.split('/spreadsheets/d/')[1].split('/')[0]
        else:
            raise ValueError(f"Could not extract spreadsheet ID from URL: {sheets_url}")
        
        return spreadsheet_id
    
    def get_sheet_data(self, spreadsheet_id: str, range_name: str = "A:N") -> List[List]:
        """
        Get data from a Google Sheet.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            range_name: Range to read (e.g., "A:N" for all columns A through N)
            
        Returns:
            List of lists representing rows and columns
        """
        result = self.service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=range_name
        ).execute()
        
        values = result.get('values', [])
        print(f"Retrieved {len(values)} rows from Google Sheets")
        return values
    
    def update_sheet_data(self, spreadsheet_id: str, range_name: str, values: List[List]) -> bool:
        """
        Update data in a Google Sheet.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            range_name: Range to update (e.g., "A1:N100")
            values: List of lists representing rows and columns
            
        Returns:
            True if successful, False otherwise
        """
        try:
            body = {
                'values': values
            }
            
            result = self.service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()
            
            updated_cells = result.get('updatedCells', 0)
            print(f"Updated {updated_cells} cells in Google Sheets")
            return True
            
        except Exception as e:
            print(f"Error updating Google Sheets: {e}")
            return False
    
    def csv_rows_to_sheet_values(self, csv_rows: List[Dict]) -> List[List]:
        """
        Convert CSV row dictionaries to Google Sheets values format.
        
        Args:
            csv_rows: List of dictionaries from CSV
            
        Returns:
            List of lists for Google Sheets
        """
        if not csv_rows:
            return []
        
        # Get headers from first row
        headers = list(csv_rows[0].keys())
        
        # Convert to list of lists
        values = [headers]  # Header row
        
        for row in csv_rows:
            row_values = []
            for header in headers:
                row_values.append(row.get(header, ''))
            values.append(row_values)
        
        return values
    
    def update_image_columns_only(self, spreadsheet_id: str, csv_rows: List[Dict]) -> bool:
        """
        Update only the image columns in Google Sheets.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            csv_rows: List of CSV row dictionaries
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current sheet data to find the right columns
            current_data = self.get_sheet_data(spreadsheet_id)
            
            if not current_data:
                print("Error: Could not retrieve current sheet data")
                return False
            
            headers = current_data[0] if current_data else []
            
            # Find image column indices
            image_columns = ['IMG-Angle', 'IMG-Front', 'IMG-Top', 'IMG-Lifestyle']
            column_indices = {}
            
            for i, header in enumerate(headers):
                if header in image_columns:
                    column_indices[header] = i
            
            if not column_indices:
                print("Error: Could not find image columns in sheet")
                return False
            
            print(f"Found image columns at indices: {column_indices}")
            
            # Prepare updates for each image column
            updates = []
            
            for col_name, col_index in column_indices.items():
                # Convert column index to letter (A=0, B=1, etc.)
                col_letter = chr(ord('A') + col_index)
                
                # Prepare values for this column (skip header row)
                column_values = []
                for i, row in enumerate(csv_rows):
                    if i == 0:  # Skip header row in CSV data
                        continue
                    column_values.append([row.get(col_name, '')])
                
                # Create range (start from row 2 to skip header)
                range_name = f"{col_letter}2:{col_letter}{len(column_values) + 1}"
                
                updates.append({
                    'range': range_name,
                    'values': column_values
                })
            
            # Batch update all image columns
            body = {
                'valueInputOption': 'RAW',
                'data': updates
            }
            
            result = self.service.spreadsheets().values().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=body
            ).execute()
            
            total_updated = sum(reply.get('updatedCells', 0) for reply in result.get('replies', []))
            print(f"Updated {total_updated} cells across {len(updates)} image columns")
            return True
            
        except Exception as e:
            print(f"Error updating image columns: {e}")
            return False
    
    def backup_sheet_data(self, spreadsheet_id: str, backup_file: str = "sheets_backup.csv") -> bool:
        """
        Create a backup of the current sheet data.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            backup_file: Local file to save backup
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import csv
            
            # Get all data
            values = self.get_sheet_data(spreadsheet_id)
            
            if not values:
                print("No data to backup")
                return False
            
            # Write to CSV
            with open(backup_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerows(values)
            
            print(f"Backed up {len(values)} rows to {backup_file}")
            return True
            
        except Exception as e:
            print(f"Error creating backup: {e}")
            return False
    
    def get_sheet_info(self, spreadsheet_id: str) -> Dict:
        """
        Get information about the spreadsheet.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            
        Returns:
            Dictionary with spreadsheet information
        """
        try:
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()
            
            info = {
                'title': spreadsheet.get('properties', {}).get('title', 'Unknown'),
                'sheets': []
            }
            
            for sheet in spreadsheet.get('sheets', []):
                sheet_props = sheet.get('properties', {})
                info['sheets'].append({
                    'title': sheet_props.get('title', 'Unknown'),
                    'sheet_id': sheet_props.get('sheetId', 0),
                    'row_count': sheet_props.get('gridProperties', {}).get('rowCount', 0),
                    'column_count': sheet_props.get('gridProperties', {}).get('columnCount', 0)
                })
            
            return info
            
        except Exception as e:
            print(f"Error getting sheet info: {e}")
            return {}


def main():
    """Test Google Sheets functionality."""
    updater = SheetsUpdater()
    
    # Test with the provided spreadsheet URL
    sheets_url = "https://docs.google.com/spreadsheets/d/1DwYiIAXlZ8c1047_KszvE3vSJC25Ds6yXjDKnb1t6go/edit?usp=sharing"
    
    try:
        spreadsheet_id = updater.extract_spreadsheet_id(sheets_url)
        print(f"Spreadsheet ID: {spreadsheet_id}")
        
        # Get sheet info
        info = updater.get_sheet_info(spreadsheet_id)
        print(f"Spreadsheet: {info.get('title', 'Unknown')}")
        
        for sheet in info.get('sheets', []):
            print(f"  Sheet: {sheet['title']} ({sheet['row_count']} rows, {sheet['column_count']} columns)")
        
        # Get first few rows
        data = updater.get_sheet_data(spreadsheet_id, "A1:N5")
        print(f"\nFirst few rows:")
        for i, row in enumerate(data[:3]):
            print(f"  Row {i+1}: {row[:5]}...")  # Show first 5 columns
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
