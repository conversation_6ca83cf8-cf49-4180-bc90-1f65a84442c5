"""
CSV processing module for SKU data and image links.
"""
import csv
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional


class CSVProcessor:
    """<PERSON>les reading and writing CSV files with SKU data."""
    
    def __init__(self, csv_path: str = "WildBreed Tasking Central - SKUs.csv"):
        self.csv_path = Path(csv_path)
        self.backup_path = self.csv_path.with_suffix('.backup.csv')
    
    def read_csv(self) -> List[Dict]:
        """
        Read CSV file and return list of dictionaries.
        
        Returns:
            List of dictionaries, one per row
        """
        if not self.csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {self.csv_path}")
        
        rows = []
        with open(self.csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                rows.append(row)
        
        print(f"Read {len(rows)} rows from {self.csv_path}")
        return rows
    
    def write_csv(self, rows: List[Dict], backup: bool = True) -> None:
        """
        Write list of dictionaries to CSV file.
        
        Args:
            rows: List of dictionaries to write
            backup: Whether to create a backup of the original file
        """
        if not rows:
            raise ValueError("No rows to write")
        
        # Create backup if requested and original exists
        if backup and self.csv_path.exists():
            import shutil
            shutil.copy2(self.csv_path, self.backup_path)
            print(f"Created backup: {self.backup_path}")
        
        # Get fieldnames from first row
        fieldnames = list(rows[0].keys())
        
        # Write CSV
        with open(self.csv_path, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        
        print(f"Wrote {len(rows)} rows to {self.csv_path}")
    
    def update_image_links(self, rows: List[Dict], image_matches: Dict[int, Dict[str, str]]) -> List[Dict]:
        """
        Update rows with image links.
        
        Args:
            rows: List of row dictionaries
            image_matches: Dictionary mapping row index to image links
            
        Returns:
            Updated list of row dictionaries
        """
        updated_rows = []
        
        for i, row in enumerate(rows):
            updated_row = row.copy()
            
            # Update with image links if available
            if i in image_matches:
                for column, link in image_matches[i].items():
                    updated_row[column] = link
            
            updated_rows.append(updated_row)
        
        return updated_rows
    
    def get_sku_summary(self, rows: List[Dict]) -> Dict:
        """
        Get summary statistics of SKU data.
        
        Args:
            rows: List of row dictionaries
            
        Returns:
            Dictionary with summary statistics
        """
        summary = {
            'total_rows': len(rows),
            'styles': set(),
            'colors': set(),
            'sizes': set(),
            'categories': set(),
            'rows_with_images': 0,
            'angle_images': 0,
            'front_images': 0,
            'top_images': 0,
            'lifestyle_images': 0
        }
        
        for row in rows:
            # Skip empty rows
            if not row.get('Style') or not row.get('Color'):
                continue
            
            summary['styles'].add(row.get('Style', '').strip())
            summary['colors'].add(row.get('Color', '').strip())
            summary['sizes'].add(row.get('Size', '').strip())
            summary['categories'].add(row.get('Category', '').strip())
            
            # Count image links
            has_images = False
            for img_type in ['IMG-Angle', 'IMG-Front', 'IMG-Top', 'IMG-Lifestyle']:
                if row.get(img_type, '').strip():
                    # Convert IMG-Angle to angle_images, etc.
                    key = img_type.split('-')[1].lower() + '_images'
                    summary[key] += 1
                    has_images = True
            
            if has_images:
                summary['rows_with_images'] += 1
        
        # Convert sets to counts
        summary['unique_styles'] = len(summary['styles'])
        summary['unique_colors'] = len(summary['colors'])
        summary['unique_sizes'] = len(summary['sizes'])
        summary['unique_categories'] = len(summary['categories'])
        
        return summary
    
    def print_summary(self, rows: List[Dict]) -> None:
        """Print summary of CSV data."""
        summary = self.get_sku_summary(rows)
        
        print(f"\n=== CSV Summary ===")
        print(f"Total rows: {summary['total_rows']}")
        print(f"Unique styles: {summary['unique_styles']}")
        print(f"Unique colors: {summary['unique_colors']}")
        print(f"Unique sizes: {summary['unique_sizes']}")
        print(f"Unique categories: {summary['unique_categories']}")
        print(f"\nImage Links:")
        print(f"  Rows with images: {summary['rows_with_images']}")
        print(f"  Angle images: {summary['angle_images']}")
        print(f"  Front images: {summary['front_images']}")
        print(f"  Top images: {summary['top_images']}")
        print(f"  Lifestyle images: {summary['lifestyle_images']}")
    
    def validate_csv_structure(self, rows: List[Dict]) -> bool:
        """
        Validate that CSV has expected structure.
        
        Args:
            rows: List of row dictionaries
            
        Returns:
            True if structure is valid
        """
        if not rows:
            print("Error: CSV is empty")
            return False
        
        required_columns = [
            'Style', 'Color', 'Size', 'Count', 'Note', 'Category',
            'ColorCode', 'ColorSnake', 'StyleCode', 'SKU_BASE',
            'IMG-Angle', 'IMG-Front', 'IMG-Top', 'IMG-Lifestyle'
        ]
        
        first_row = rows[0]
        missing_columns = []
        
        for col in required_columns:
            if col not in first_row:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"Error: Missing required columns: {missing_columns}")
            return False
        
        print("✅ CSV structure is valid")
        return True
    
    def find_duplicate_skus(self, rows: List[Dict]) -> List[Dict]:
        """
        Find duplicate SKU entries.
        
        Args:
            rows: List of row dictionaries
            
        Returns:
            List of duplicate row dictionaries
        """
        seen_skus = {}
        duplicates = []
        
        for i, row in enumerate(rows):
            style = row.get('Style', '').strip()
            color = row.get('Color', '').strip()
            size = row.get('Size', '').strip()
            
            if not style or not color:
                continue
            
            sku_key = f"{style}-{color}-{size}"
            
            if sku_key in seen_skus:
                duplicates.append({
                    'row_index': i,
                    'sku_key': sku_key,
                    'first_occurrence': seen_skus[sku_key],
                    'row_data': row
                })
            else:
                seen_skus[sku_key] = i
        
        if duplicates:
            print(f"Found {len(duplicates)} duplicate SKUs")
            for dup in duplicates[:5]:  # Show first 5
                print(f"  {dup['sku_key']} (rows {dup['first_occurrence']} and {dup['row_index']})")
        
        return duplicates


def main():
    """Test CSV processing functionality."""
    processor = CSVProcessor()
    
    try:
        # Read CSV
        rows = processor.read_csv()
        
        # Validate structure
        if not processor.validate_csv_structure(rows):
            return
        
        # Print summary
        processor.print_summary(rows)
        
        # Check for duplicates
        duplicates = processor.find_duplicate_skus(rows)
        
        # Show first few rows
        print(f"\nFirst 3 rows:")
        for i, row in enumerate(rows[:3]):
            if row.get('Style'):  # Skip empty rows
                print(f"  {i+1}. {row['Style']} - {row['Color']} ({row['Size']})")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
