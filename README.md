# Google Drive Hat Image Scanner

Automatically scan Google Drive folders for hat images and update SKU data with image links.

## 📁 Data Sources

- **Google Drive Folder**: <https://drive.google.com/drive/folders/1RrUSrvgUfEyO3zvwqsNXmYZUPd08ZDGs?usp=sharing>
- **Local CSV**: `WildBreed Tasking Central - SKUs.csv`
- **Google Sheets**: <https://docs.google.com/spreadsheets/d/1DwYiIAXlZ8c1047_KszvE3vSJC25Ds6yXjDKnb1t6go/edit?usp=sharing>

## 🚀 Quick Start

1. **Install dependencies**:

   ```bash
   uv sync
   ```

2. **Set up Google API credentials** (see [Authentication Setup](#-authentication-setup))

3. **Test authentication**:

   ```bash
   uv run python test_auth.py
   ```

4. **Run the complete workflow**:

   ```bash
   uv run python main.py run-all
   ```

## 🔐 Authentication Setup

### Step 1: Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the required APIs:
   - [Google Drive API](https://console.cloud.google.com/apis/library/drive.googleapis.com)
   - [Google Sheets API](https://console.cloud.google.com/apis/library/sheets.googleapis.com)

### Step 2: Create OAuth Credentials

1. Go to [Credentials](https://console.cloud.google.com/apis/credentials)
2. Click "Create Credentials" → "OAuth client ID"
3. Choose "Desktop application" as the application type
4. Download the credentials JSON file
5. Save it as `credentials.json` in the project root directory

### Step 3: Test Authentication

```bash
uv run python test_auth.py
```

On first run, this will open a browser window for OAuth authorization.

## 📋 Usage

### Command Line Interface

The main script provides several commands:

```bash
# Test authentication
uv run python main.py auth

# Scan Google Drive folder for images
uv run python main.py scan

# Analyze local CSV file
uv run python main.py analyze-csv

# Match SKUs with images (dry run)
uv run python main.py match --dry-run

# Match and update CSV file
uv run python main.py match

# Update Google Sheets with CSV data
uv run python main.py update-sheets

# Run complete workflow
uv run python main.py run-all
```

### Complete Workflow

The `run-all` command performs the entire process:

1. **Authentication**: Verifies Google API access
2. **Drive Scan**: Scans the Google Drive folder for images
3. **CSV Analysis**: Reads and validates the local CSV file
4. **Image Matching**: Matches images to SKUs based on style and color
5. **CSV Update**: Updates the local CSV with image links
6. **Sheets Update**: Updates the Google Sheets with the new data

## 🖼️ Image Matching Logic

### Expected Image Filename Formats

The scanner looks for images with these naming patterns:

- `HAT-STYLE-COLOR-TYPE.webp` (e.g., `HAT-SOLANA-SALMON-ANGLE.webp`)
- `STYLE-COLOR-TYPE.webp` (e.g., `SOLANA-SALMON-ANGLE.webp`)

### Image Types

- **ANGLE**: Side/angled view of the hat
- **FRONT**: Front view of the hat
- **TOP**: Top-down view of the hat
- **LIFESTYLE**: Lifestyle/worn image

### Style and Color Matching

The system uses fuzzy matching and aliases to handle variations in naming:

- **Styles**: SOLANA, ENCINITAS, DEL_MAR, LEUCADIA, NORFOLK, HELENA, EXPLORER
- **Colors**: Supports 30+ color variations with aliases (e.g., DKBRN → DARK_BROWN)

## 📊 CSV Structure

The CSV file should have these columns:

| Column | Description |
|--------|-------------|
| Style | Hat style name |
| Color | Hat color name |
| Size | Hat size |
| Count | Inventory count |
| SKU_BASE | Base SKU identifier |
| IMG-Angle | Link to angle image |
| IMG-Front | Link to front image |
| IMG-Top | Link to top image |
| IMG-Lifestyle | Link to lifestyle image |

## 🔧 Configuration

Default URLs and files are configured in `main.py`:

```python
DRIVE_FOLDER_URL = "https://drive.google.com/drive/folders/..."
SHEETS_URL = "https://docs.google.com/spreadsheets/d/..."
CSV_FILE = "WildBreed Tasking Central - SKUs.csv"
```

You can override these with command-line options:

```bash
uv run python main.py run-all --csv-file "custom.csv" --folder-url "https://..."
```

## 🛠️ Development

### Project Structure

```text
├── src/
│   ├── auth.py              # Google API authentication
│   ├── drive_scanner.py     # Google Drive folder scanning
│   ├── image_matcher.py     # SKU to image matching logic
│   ├── csv_processor.py     # CSV file handling
│   └── sheets_updater.py    # Google Sheets integration
├── main.py                  # Main CLI application
├── test_auth.py            # Authentication test script
├── pyproject.toml          # Project dependencies
└── README.md               # This file
```

### Running Individual Components

```bash
# Test Drive scanning
uv run python -m src.drive_scanner

# Test CSV processing
uv run python -m src.csv_processor

# Test Sheets integration
uv run python -m src.sheets_updater
```

## 📝 Notes

- **Image Format**: The system prioritizes `.webp` format for smaller file sizes
- **Backup**: Automatic backups are created before updating files
- **Error Handling**: Comprehensive error handling with helpful messages
- **Dry Run**: Use `--dry-run` flag to preview changes without updating files

## 🐛 Troubleshooting

### Authentication Issues

- Ensure `credentials.json` is in the project root
- Check that APIs are enabled in Google Cloud Console
- Try deleting `token.pickle` and re-authenticating

### Image Matching Issues

- Check image filename formats match expected patterns
- Review style and color aliases in `image_matcher.py`
- Use `--dry-run` to see matching results before updating

### Permission Issues

- Ensure Google Drive folder has "Anyone with link can view" permissions
- Verify Google Sheets has appropriate sharing permissions
