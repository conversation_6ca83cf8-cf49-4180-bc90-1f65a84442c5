Google Drive Folder holding all uploaded hat images for product inventory:
- https://drive.google.com/drive/folders/1RrUSrvgUfEyO3zvwqsNXmYZUPd08ZDGs?usp=sharing


SKUs to match:
- Local: "WildBreed Tasking Central - SKUs.csv"
- Google Drive: https://docs.google.com/spreadsheets/d/1DwYiIAXlZ8c1047_KszvE3vSJC25Ds6yXjDKnb1t6go/edit?usp=sharing

# Python Usage

Always uv instead of pip.

Link the .webp format of the images (not the .png) (smaller size).

# TODO:

Scan the drive and get links for 
1. IMG-Angle
2. IMG-Front
3. IMG-Top

Update the CSV and I'll copy/paste it to the other google drive.


