#!/usr/bin/env python3
"""
Main script for scanning Google Drive and updating SKU data with image links.
"""
import sys
from pathlib import Path

import click

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.auth import verify_authentication
from src.drive_scanner import DriveScanner
from src.image_matcher import ImageMatcher
from src.csv_processor import CSVProcessor
from src.sheets_updater import SheetsUpdater


# Configuration
DRIVE_FOLDER_URL = "https://drive.google.com/drive/folders/1RrUSrvgUfEyO3zvwqsNXmYZUPd08ZDGs?usp=sharing"
SHEETS_URL = "https://docs.google.com/spreadsheets/d/1DwYiIAXlZ8c1047_KszvE3vSJC25Ds6yXjDKnb1t6go/edit?usp=sharing"
CSV_FILE = "WildBreed Tasking Central - SKUs.csv"


@click.group()
def cli():
    """Google Drive hat image scanner and SKU updater."""
    pass


@cli.command()
def auth():
    """Test Google API authentication."""
    click.echo("🔐 Testing Google API authentication...")
    
    if verify_authentication():
        click.echo("✅ Authentication successful!")
    else:
        click.echo("❌ Authentication failed!")
        click.echo("\nTo set up authentication:")
        click.echo("1. Go to Google Cloud Console")
        click.echo("2. Create a new project or select existing one")
        click.echo("3. Enable Google Drive API and Google Sheets API")
        click.echo("4. Create OAuth 2.0 credentials")
        click.echo("5. Download credentials.json to project root")
        sys.exit(1)


@cli.command()
@click.option('--folder-url', default=DRIVE_FOLDER_URL, help='Google Drive folder URL')
def scan(folder_url):
    """Scan Google Drive folder for images."""
    click.echo(f"📁 Scanning Google Drive folder...")
    
    try:
        scanner = DriveScanner()
        folder_id = scanner.extract_folder_id(folder_url)
        
        click.echo(f"Folder ID: {folder_id}")
        
        # Scan for images
        images = scanner.scan_folder(folder_id)
        click.echo(f"Found {len(images)} images")
        
        # Categorize images
        categorized = scanner.categorize_images(images)
        
        click.echo(f"\n📊 Image Summary:")
        for style, colors in categorized.items():
            click.echo(f"  {style}: {len(colors)} colors")
            for color, types in colors.items():
                type_list = ', '.join(types.keys())
                click.echo(f"    {color}: {type_list}")
        
        # Show some examples
        click.echo(f"\n🖼️  Sample Images:")
        count = 0
        for style, colors in categorized.items():
            for color, types in colors.items():
                for img_type, link in types.items():
                    click.echo(f"  {style}-{color}-{img_type}: {link}")
                    count += 1
                    if count >= 5:
                        break
                if count >= 5:
                    break
            if count >= 5:
                break
        
    except Exception as e:
        click.echo(f"❌ Error scanning folder: {e}")
        sys.exit(1)


@cli.command()
@click.option('--csv-file', default=CSV_FILE, help='CSV file path')
def analyze_csv(csv_file):
    """Analyze local CSV file."""
    click.echo(f"📋 Analyzing CSV file: {csv_file}")
    
    try:
        processor = CSVProcessor(csv_file)
        rows = processor.read_csv()
        
        if not processor.validate_csv_structure(rows):
            sys.exit(1)
        
        processor.print_summary(rows)
        
        # Check for duplicates
        duplicates = processor.find_duplicate_skus(rows)
        if duplicates:
            click.echo(f"\n⚠️  Found {len(duplicates)} duplicate SKUs")
        
    except Exception as e:
        click.echo(f"❌ Error analyzing CSV: {e}")
        sys.exit(1)


@cli.command()
@click.option('--folder-url', default=DRIVE_FOLDER_URL, help='Google Drive folder URL')
@click.option('--csv-file', default=CSV_FILE, help='CSV file path')
@click.option('--dry-run', is_flag=True, help='Show matches without updating files')
def match(folder_url, csv_file, dry_run):
    """Match SKUs with Google Drive images."""
    click.echo(f"🔍 Matching SKUs with images...")
    
    try:
        # Scan Drive folder
        click.echo("Scanning Google Drive...")
        scanner = DriveScanner()
        folder_id = scanner.extract_folder_id(folder_url)
        images = scanner.scan_folder(folder_id)
        categorized = scanner.categorize_images(images)
        
        # Read CSV
        click.echo("Reading CSV file...")
        processor = CSVProcessor(csv_file)
        rows = processor.read_csv()
        
        if not processor.validate_csv_structure(rows):
            sys.exit(1)
        
        # Match images to SKUs
        click.echo("Matching images to SKUs...")
        matcher = ImageMatcher()
        matched_rows = matcher.match_all_skus(rows, categorized)
        
        # Show unmatched images
        unmatched = matcher.find_unmatched_images(rows, categorized)
        if unmatched:
            click.echo(f"\n🔍 Unmatched Images:")
            for style, colors in unmatched.items():
                for color, types in colors.items():
                    type_list = ', '.join(types.keys())
                    click.echo(f"  {style}-{color}: {type_list}")
        
        if dry_run:
            click.echo("\n🔍 Dry run - no files updated")
            
            # Show some examples of matches
            click.echo("\nSample matches:")
            count = 0
            for row in matched_rows:
                if row.get('IMG-Angle') or row.get('IMG-Front') or row.get('IMG-Top'):
                    style = row.get('Style', '')
                    color = row.get('Color', '')
                    click.echo(f"  {style}-{color}:")
                    for img_type in ['IMG-Angle', 'IMG-Front', 'IMG-Top']:
                        if row.get(img_type):
                            click.echo(f"    {img_type}: {row[img_type]}")
                    count += 1
                    if count >= 3:
                        break
        else:
            # Update CSV file
            click.echo("Updating CSV file...")
            processor.write_csv(matched_rows)
            click.echo("✅ CSV file updated!")
        
    except Exception as e:
        click.echo(f"❌ Error matching images: {e}")
        sys.exit(1)


@cli.command()
@click.option('--sheets-url', default=SHEETS_URL, help='Google Sheets URL')
@click.option('--csv-file', default=CSV_FILE, help='CSV file path')
@click.option('--backup', is_flag=True, default=True, help='Create backup before updating')
def update_sheets(sheets_url, csv_file, backup):
    """Update Google Sheets with CSV data."""
    click.echo(f"📊 Updating Google Sheets...")
    
    try:
        # Read CSV
        processor = CSVProcessor(csv_file)
        rows = processor.read_csv()
        
        if not processor.validate_csv_structure(rows):
            sys.exit(1)
        
        # Update Google Sheets
        updater = SheetsUpdater()
        spreadsheet_id = updater.extract_spreadsheet_id(sheets_url)
        
        click.echo(f"Spreadsheet ID: {spreadsheet_id}")
        
        # Get sheet info
        info = updater.get_sheet_info(spreadsheet_id)
        click.echo(f"Updating: {info.get('title', 'Unknown')}")
        
        # Create backup if requested
        if backup:
            click.echo("Creating backup...")
            if not updater.backup_sheet_data(spreadsheet_id, "sheets_backup.csv"):
                click.echo("⚠️  Backup failed, continuing anyway...")
        
        # Update only image columns
        click.echo("Updating image columns...")
        if updater.update_image_columns_only(spreadsheet_id, rows):
            click.echo("✅ Google Sheets updated!")
        else:
            click.echo("❌ Failed to update Google Sheets")
            sys.exit(1)
        
    except Exception as e:
        click.echo(f"❌ Error updating sheets: {e}")
        sys.exit(1)


@cli.command()
@click.option('--folder-url', default=DRIVE_FOLDER_URL, help='Google Drive folder URL')
@click.option('--csv-file', default=CSV_FILE, help='CSV file path')
@click.option('--sheets-url', default=SHEETS_URL, help='Google Sheets URL')
@click.option('--skip-sheets', is_flag=True, help='Skip Google Sheets update')
def run_all(folder_url, csv_file, sheets_url, skip_sheets):
    """Run the complete workflow: scan, match, and update."""
    click.echo("🚀 Running complete workflow...")
    
    # Test authentication first
    click.echo("1. Testing authentication...")
    if not verify_authentication():
        click.echo("❌ Authentication failed!")
        sys.exit(1)
    
    try:
        # Scan Drive folder
        click.echo("2. Scanning Google Drive...")
        scanner = DriveScanner()
        folder_id = scanner.extract_folder_id(folder_url)
        images = scanner.scan_folder(folder_id)
        categorized = scanner.categorize_images(images)
        
        # Read and validate CSV
        click.echo("3. Reading CSV file...")
        processor = CSVProcessor(csv_file)
        rows = processor.read_csv()
        
        if not processor.validate_csv_structure(rows):
            sys.exit(1)
        
        # Match images to SKUs
        click.echo("4. Matching images to SKUs...")
        matcher = ImageMatcher()
        matched_rows = matcher.match_all_skus(rows, categorized)
        
        # Update CSV file
        click.echo("5. Updating CSV file...")
        processor.write_csv(matched_rows)
        
        # Update Google Sheets
        if not skip_sheets:
            click.echo("6. Updating Google Sheets...")
            updater = SheetsUpdater()
            spreadsheet_id = updater.extract_spreadsheet_id(sheets_url)
            
            # Create backup
            updater.backup_sheet_data(spreadsheet_id, "sheets_backup.csv")
            
            # Update sheets
            if updater.update_image_columns_only(spreadsheet_id, matched_rows):
                click.echo("✅ Google Sheets updated!")
            else:
                click.echo("⚠️  Google Sheets update failed")
        
        click.echo("\n🎉 Workflow completed successfully!")
        
        # Show summary
        processor.print_summary(matched_rows)
        
    except Exception as e:
        click.echo(f"❌ Workflow failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    cli()
